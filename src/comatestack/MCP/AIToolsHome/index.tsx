import {Button} from '@panda-design/components';
import {MCPSquareLink, MCPPlaygroundLink} from '@/links/mcp';
import {IconArrowRight} from '@/icons/mcp';
import mcpAIToolsTitle from '@/assets/mcp/mcpAIToolsTitle.svg';
import {features, producerSteps, consumerSteps} from './constants';
import {ProcessCardComponent} from './components';
import {
    Container,
    TopNavArea,
    NavLink,
    MainTitleContainer,
    Description,
    ButtonArea,
    FeaturesContainer,
    FeatureCard,
    FeatureTitle,
    FeatureContent,
    IconWrapper,
    ProcessContainer,
} from './styles';

const AIToolsHome = () => {
    return (
        <Container>
            <TopNavArea>
                <NavLink href="#">使用文档<IconArrowRight style={{color: '#0080FF'}} /></NavLink>
                <NavLink href="#">介绍文档<IconArrowRight style={{color: '#0080FF'}} /></NavLink>
            </TopNavArea>

            <MainTitleContainer>
                <img src={mcpAIToolsTitle} alt="MCP AI Tools Title" />
                <Description>
                    厂内MCP服务中心：一站式解决AI Agent工具生产与调用难题
                </Description>
                <ButtonArea>
                    <MCPSquareLink>
                        <Button type="primary">去MCP广场体验</Button>
                    </MCPSquareLink>
                    <MCPPlaygroundLink>
                        <Button>去Playground试用</Button>
                    </MCPPlaygroundLink>
                </ButtonArea>
            </MainTitleContainer>

            <FeaturesContainer>
                {features.map((feature, index) => {
                    const IconComponent = feature.icon;
                    return (
                        <FeatureCard key={index}>
                            <FeatureTitle>{feature.title}</FeatureTitle>
                            <FeatureContent>{feature.content}</FeatureContent>
                            <IconWrapper>
                                <IconComponent style={{fontSize: 56}} />
                            </IconWrapper>
                        </FeatureCard>
                    );
                })}
            </FeaturesContainer>

            <ProcessContainer>
                <ProcessCardComponent title="工具生产者使用流程" steps={producerSteps} />
                <ProcessCardComponent title="工具消费者使用流程" steps={consumerSteps} />
            </ProcessContainer>
        </Container>
    );
};

export default AIToolsHome;
